const Imap = require('imap');
const { simpleParser } = require('mailparser');

class ImapReceiver {
    constructor() {
        this.imap = new Imap({
            user: '<EMAIL>',
            password: 'okwbkuwqovuschdg',
            host: 'imap.qq.com',
            port: 993,
            tls: true,
            tlsOptions: {
                rejectUnauthorized: false
            }
        });

        this.imap.once('ready', () => {
            console.log('IMAP连接已建立');
        });

        this.imap.once('error', (err) => {
            console.error('IMAP连接错误:', err);
        });

        this.imap.once('end', () => {
            console.log('IMAP连接已关闭');
        });
    }

    /**
     * 连接到IMAP服务器
     * @returns {Promise} 连接Promise
     */
    connect() {
        return new Promise((resolve, reject) => {
            this.imap.once('ready', resolve);
            this.imap.once('error', reject);
            this.imap.connect();
        });
    }

    /**
     * 断开IMAP连接
     */
    disconnect() {
        this.imap.end();
    }

    /**
     * 获取最新的邮件
     * @param {number} count - 获取邮件数量，默认为5
     * @returns {Promise<Array>} 邮件数组
     */
    getLatestEmails(count = 5) {
        return new Promise((resolve, reject) => {
            this.imap.openBox('INBOX', true, (err, box) => {
                if (err) {
                    reject(err);
                    return;
                }

                const total = box.messages.total;
                const start = Math.max(1, total - count + 1);
                const end = total;

                if (total === 0) {
                    resolve([]);
                    return;
                }

                const fetch = this.imap.seq.fetch(`${start}:${end}`, {
                    bodies: '',
                    struct: true
                });

                const emails = [];

                fetch.on('message', (msg, seqno) => {
                    let buffer = '';
                    
                    msg.on('body', (stream, info) => {
                        stream.on('data', (chunk) => {
                            buffer += chunk.toString('utf8');
                        });
                    });

                    msg.once('end', () => {
                        simpleParser(buffer, (err, parsed) => {
                            if (err) {
                                console.error('解析邮件错误:', err);
                                return;
                            }
                            
                            emails.push({
                                subject: parsed.subject,
                                from: parsed.from,
                                date: parsed.date,
                                text: parsed.text,
                                html: parsed.html
                            });
                        });
                    });
                });

                fetch.once('error', (err) => {
                    reject(err);
                });

                fetch.once('end', () => {
                    setTimeout(() => {
                        resolve(emails);
                    }, 1000); // 等待邮件解析完成
                });
            });
        });
    }

    /**
     * 搜索包含特定内容的邮件
     * @param {string} searchText - 搜索文本
     * @param {number} days - 搜索最近几天的邮件，默认为1天
     * @returns {Promise<Array>} 匹配的邮件数组
     */
    searchEmails(searchText, days = 1) {
        return new Promise((resolve, reject) => {
            this.imap.openBox('INBOX', true, (err, box) => {
                if (err) {
                    reject(err);
                    return;
                }

                const since = new Date();
                since.setDate(since.getDate() - days);

                this.imap.search([
                    ['SINCE', since],
                    ['BODY', searchText]
                ], (err, results) => {
                    if (err) {
                        reject(err);
                        return;
                    }

                    if (!results || results.length === 0) {
                        resolve([]);
                        return;
                    }

                    const fetch = this.imap.fetch(results, {
                        bodies: '',
                        struct: true
                    });

                    const emails = [];

                    fetch.on('message', (msg, seqno) => {
                        let buffer = '';
                        
                        msg.on('body', (stream, info) => {
                            stream.on('data', (chunk) => {
                                buffer += chunk.toString('utf8');
                            });
                        });

                        msg.once('end', () => {
                            simpleParser(buffer, (err, parsed) => {
                                if (err) {
                                    console.error('解析邮件错误:', err);
                                    return;
                                }
                                
                                emails.push({
                                    subject: parsed.subject,
                                    from: parsed.from,
                                    date: parsed.date,
                                    text: parsed.text,
                                    html: parsed.html
                                });
                            });
                        });
                    });

                    fetch.once('error', (err) => {
                        reject(err);
                    });

                    fetch.once('end', () => {
                        setTimeout(() => {
                            resolve(emails);
                        }, 1000);
                    });
                });
            });
        });
    }
}

module.exports = ImapReceiver;

const EmailVerificationPlugin = require('./index');

/**
 * 实时验证码获取测试
 * 这个测试演示了如何获取最新收到的验证码
 */

async function testRealtimeVerificationCode() {
    console.log('🚀 实时验证码获取测试\n');
    
    const plugin = new EmailVerificationPlugin();
    
    try {
        // 生成测试邮箱
        const testEmail = plugin.generateRandomEmail();
        console.log(`📧 生成测试邮箱: ${testEmail}\n`);
        
        // 连接到邮箱服务器
        console.log('🔗 连接到邮箱服务器...');
        await plugin.connect();
        console.log('✅ 连接成功!\n');
        
        // 测试1: 获取最新一封邮件的验证码
        console.log('📬 测试1: 获取最新一封邮件的验证码');
        const latestResult = await plugin.getLatestEmailVerificationCode();
        
        if (latestResult) {
            console.log('✅ 找到验证码!');
            console.log(`   验证码: ${latestResult.verificationCode}`);
            console.log(`   邮件主题: ${latestResult.subject}`);
            console.log(`   发件人: ${latestResult.from?.text || latestResult.from}`);
            console.log(`   时间: ${latestResult.date}\n`);
        } else {
            console.log('❌ 最新邮件中未找到验证码\n');
        }
        
        // 测试2: 对比传统方式（检查多封邮件）
        console.log('📬 测试2: 传统方式（检查最新5封邮件）');
        const traditionalResult = await plugin.getLatestVerificationCode(5);
        
        if (traditionalResult) {
            console.log('✅ 找到验证码!');
            console.log(`   验证码: ${traditionalResult.verificationCode}`);
            console.log(`   邮件主题: ${traditionalResult.subject}`);
            console.log(`   发件人: ${traditionalResult.from?.text || traditionalResult.from}`);
            console.log(`   时间: ${traditionalResult.date}\n`);
        } else {
            console.log('❌ 未找到验证码\n');
        }
        
        // 测试3: 实时等待模式（只检查最新邮件）
        console.log('⏰ 测试3: 实时等待模式演示');
        console.log('提示: 现在可以向 <EMAIL> 发送包含验证码的邮件');
        console.log('邮件内容示例: "Your verification code is: 123456"\n');
        
        const realtimeResult = await plugin.waitForVerificationCode(
            5000,  // 5秒轮询一次
            30000, // 最大等待30秒
            true   // 只检查最新邮件
        );
        
        if (realtimeResult) {
            console.log('🎉 实时获取成功!');
            console.log(`   验证码: ${realtimeResult.verificationCode}`);
            console.log(`   邮件主题: ${realtimeResult.subject}`);
            console.log(`   时间: ${realtimeResult.date}\n`);
        } else {
            console.log('⏰ 等待超时，未收到新的验证码邮件\n');
        }
        
        // 断开连接
        plugin.disconnect();
        console.log('🔌 已断开邮箱连接');
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error.message);
        plugin.disconnect();
    }
}

async function testPerformanceComparison() {
    console.log('\n⚡ 性能对比测试\n');
    
    const plugin = new EmailVerificationPlugin();
    
    try {
        await plugin.connect();
        
        // 测试实时模式性能
        console.log('🏃‍♂️ 测试实时模式（只检查最新邮件）...');
        const startTime1 = Date.now();
        await plugin.getLatestEmailVerificationCode();
        const time1 = Date.now() - startTime1;
        console.log(`   耗时: ${time1}ms\n`);
        
        // 测试传统模式性能
        console.log('🚶‍♂️ 测试传统模式（检查最新10封邮件）...');
        const startTime2 = Date.now();
        await plugin.getLatestVerificationCode(10);
        const time2 = Date.now() - startTime2;
        console.log(`   耗时: ${time2}ms\n`);
        
        console.log('📊 性能对比结果:');
        console.log(`   实时模式: ${time1}ms`);
        console.log(`   传统模式: ${time2}ms`);
        console.log(`   性能提升: ${((time2 - time1) / time2 * 100).toFixed(1)}%\n`);
        
        plugin.disconnect();
        
    } catch (error) {
        console.error('❌ 性能测试出错:', error.message);
        plugin.disconnect();
    }
}

// 主函数
async function main() {
    console.log('🧪 邮箱验证码插件 - 实时获取功能测试\n');
    console.log('功能改进:');
    console.log('  ✓ 优先获取最新一封邮件的验证码');
    console.log('  ✓ 按时间倒序排列邮件（最新的在前）');
    console.log('  ✓ 实时轮询模式，更快响应新邮件');
    console.log('  ✓ 性能优化，减少不必要的邮件检查\n');
    
    // 检查命令行参数
    const args = process.argv.slice(2);
    
    if (args.includes('--performance')) {
        await testPerformanceComparison();
    } else {
        await testRealtimeVerificationCode();
    }
    
    console.log('🎉 测试完成!');
}

// 运行测试
if (require.main === module) {
    main().catch(error => {
        console.error('测试运行失败:', error);
        process.exit(1);
    });
}

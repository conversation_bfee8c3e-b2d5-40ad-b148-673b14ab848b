# 邮箱验证码插件

一个用于生成随机邮箱地址（@shusj.xyz后缀）并实时监听QQ邮箱获取验证码的Node.js插件。

## 使用场景

1. **生成随机邮箱地址**（如 `<EMAIL>`）
2. **使用该邮箱地址注册网站**
3. **网站发送验证码到QQ邮箱**（<EMAIL>）
4. **插件自动监听并获取最新验证码**

## 功能特性

- ✅ 生成随机邮箱地址，后缀为 `@shusj.xyz`
- ✅ 连接到QQ邮箱IMAP服务器实时监听新邮件
- ✅ 自动提取邮件中的6位数验证码
- ✅ 智能识别新邮件，避免获取历史验证码
- ✅ 支持快速轮询模式，2-3秒响应新邮件
- ✅ 支持多种验证码格式匹配
- ✅ 批量生成邮箱地址

## 安装依赖

```bash
npm install
```

## 快速开始

### 完整使用流程

```javascript
const EmailVerificationPlugin = require('./index');

async function registerAndGetCode() {
    const plugin = new EmailVerificationPlugin();

    try {
        // 步骤1: 生成随机邮箱地址
        const email = plugin.generateRandomEmail();
        console.log('生成的邮箱:', email); // 例如: <EMAIL>

        // 步骤2: 使用该邮箱地址去注册网站
        console.log('请使用此邮箱地址注册网站，然后点击发送验证码');

        // 步骤3: 监听新的验证码邮件
        const result = await plugin.getVerificationCodeFlow(email, {
            pollInterval: 3000,   // 3秒检查一次新邮件
            maxWaitTime: 180000   // 最大等待3分钟
        });

        // 步骤4: 获取结果
        if (result.success) {
            console.log('✅ 成功获取验证码!');
            console.log('邮箱:', result.email);
            console.log('验证码:', result.verificationCode);
        } else {
            console.log('❌ 未收到验证码');
        }

    } catch (error) {
        console.error('错误:', error);
    }
}

registerAndGetCode();
```

### 快速模式（注册后立即获取）

```javascript
async function quickGetCode() {
    const plugin = new EmailVerificationPlugin();

    const email = plugin.generateRandomEmail();
    console.log('邮箱地址:', email);

    // 假设您已经完成注册，现在快速获取验证码
    const result = await plugin.quickGetVerificationCode(email, {
        pollInterval: 2000,   // 2秒检查一次，更快响应
        maxWaitTime: 60000    // 1分钟超时
    });

    if (result.success) {
        console.log('验证码:', result.verificationCode);
    }
}
```

### 批量生成邮箱

```javascript
const plugin = new EmailVerificationPlugin();

// 生成多个邮箱地址
const emails = plugin.generateMultipleEmails(5, 8);
console.log(emails);
// 输出: ['<EMAIL>', '<EMAIL>', ...]
```

## API 文档

### EmailVerificationPlugin

主插件类，提供完整的邮箱验证码功能。

#### 方法

##### `generateRandomEmail(length = 8)`
生成随机邮箱地址。

- `length`: 用户名长度，默认8位
- 返回: 完整的邮箱地址字符串

##### `generateMultipleEmails(count = 1, length = 8)`
生成多个随机邮箱地址。

- `count`: 生成数量
- `length`: 用户名长度
- 返回: 邮箱地址数组

##### `connect()`
连接到IMAP服务器。

- 返回: Promise

##### `disconnect()`
断开IMAP连接。

##### `getLatestVerificationCode(emailCount = 10, maxWaitTime = 30000)`
获取最新的验证码。

- `emailCount`: 检查的邮件数量
- `maxWaitTime`: 最大等待时间（毫秒）
- 返回: Promise<Object|null>

##### `waitForNewVerificationCode(pollInterval = 3000, maxWaitTime = 300000)`
等待新的验证码邮件（推荐使用）。

- `pollInterval`: 轮询间隔（毫秒）
- `maxWaitTime`: 最大等待时间（毫秒）
- 返回: Promise<Object|null>

##### `quickGetVerificationCode(generatedEmail, options = {})`
快速获取验证码（用于注册后立即获取）。

- `generatedEmail`: 生成的邮箱地址
- `options`: 配置选项
- 返回: Promise<Object>

##### `getVerificationCodeFlow(generatedEmail = null, options = {})`
完整的验证码获取流程。

- `generatedEmail`: 可选的邮箱地址
- `options`: 配置选项 `{pollInterval, maxWaitTime}`
- 返回: Promise<Object>

## 配置信息

插件使用以下IMAP配置连接到QQ邮箱：

- **服务器**: imap.qq.com
- **端口**: 993
- **加密**: TLS
- **邮箱**: <EMAIL>
- **授权码**: okwbkuwqovuschdg

## 验证码格式

插件支持以下验证码格式：

- `Your verification code is: 602238`
- `verification code is: 123456`
- `验证码：602238`
- `code: 123456`
- `602238 is your verification code`

## 运行示例

### 实时获取演示
```bash
node example-realtime.js
```

### 快速模式演示
```bash
node example-realtime.js --quick
```

### 批量生成演示
```bash
node example-realtime.js --batch
```

### 持续监听演示
```bash
node example-realtime.js --monitor
```

### 所有演示
```bash
node example-realtime.js --all
```

### 传统示例
```bash
node example.js
```

## 运行测试

```bash
npm test
# 或
node test.js
```

## 文件结构

```
email-verification-plugin/
├── index.js                    # 主插件文件
├── emailGenerator.js           # 邮箱生成器
├── imapReceiver.js            # IMAP邮件接收器
├── verificationCodeExtractor.js # 验证码提取器
├── test.js                    # 测试文件
├── example.js                 # 使用示例
├── package.json               # 项目配置
└── README.md                  # 说明文档
```

## 注意事项

1. 确保网络连接正常，能够访问QQ邮箱IMAP服务器
2. 验证码必须是6位数字格式
3. 插件会自动处理HTML邮件内容
4. 建议在生产环境中使用环境变量存储邮箱凭据
5. 轮询间隔不要设置太短，避免对服务器造成压力

## 错误处理

插件包含完善的错误处理机制：

- IMAP连接错误
- 邮件解析错误
- 验证码提取失败
- 网络超时等

## 许可证

MIT License

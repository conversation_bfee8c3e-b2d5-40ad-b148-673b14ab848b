# 邮箱验证码插件

一个用于生成随机邮箱地址（@shusj.xyz后缀）并通过IMAP接收验证码的Node.js插件。

## 功能特性

- ✅ 生成随机邮箱地址，后缀为 `@shusj.xyz`
- ✅ 连接到QQ邮箱IMAP服务器接收邮件
- ✅ 自动提取邮件中的6位数验证码
- ✅ 支持轮询等待新验证码
- ✅ 搜索历史验证码邮件
- ✅ 支持多种验证码格式匹配

## 安装依赖

```bash
npm install
```

## 快速开始

### 1. 基础使用

```javascript
const EmailVerificationPlugin = require('./index');

const plugin = new EmailVerificationPlugin();

// 生成随机邮箱
const email = plugin.generateRandomEmail();
console.log(email); // 例如: <EMAIL>

// 生成多个邮箱
const emails = plugin.generateMultipleEmails(3, 6);
console.log(emails); // ['<EMAIL>', '<EMAIL>', ...]
```

### 2. 获取验证码

```javascript
async function getCode() {
    const plugin = new EmailVerificationPlugin();
    
    try {
        // 连接邮箱
        await plugin.connect();
        
        // 获取最新验证码
        const result = await plugin.getLatestVerificationCode(5);
        
        if (result) {
            console.log('验证码:', result.verificationCode);
            console.log('邮件主题:', result.subject);
        }
        
        // 断开连接
        plugin.disconnect();
    } catch (error) {
        console.error('错误:', error);
    }
}
```

### 3. 等待验证码（轮询模式）

```javascript
async function waitForCode() {
    const plugin = new EmailVerificationPlugin();
    
    const result = await plugin.getVerificationCodeFlow(null, {
        pollInterval: 5000,   // 5秒轮询一次
        maxWaitTime: 300000,  // 最大等待5分钟
        emailCount: 10        // 每次检查10封邮件
    });
    
    if (result.success) {
        console.log('邮箱:', result.email);
        console.log('验证码:', result.verificationCode);
    }
}
```

## API 文档

### EmailVerificationPlugin

主插件类，提供完整的邮箱验证码功能。

#### 方法

##### `generateRandomEmail(length = 8)`
生成随机邮箱地址。

- `length`: 用户名长度，默认8位
- 返回: 完整的邮箱地址字符串

##### `generateMultipleEmails(count = 1, length = 8)`
生成多个随机邮箱地址。

- `count`: 生成数量
- `length`: 用户名长度
- 返回: 邮箱地址数组

##### `connect()`
连接到IMAP服务器。

- 返回: Promise

##### `disconnect()`
断开IMAP连接。

##### `getLatestVerificationCode(emailCount = 10, maxWaitTime = 30000)`
获取最新的验证码。

- `emailCount`: 检查的邮件数量
- `maxWaitTime`: 最大等待时间（毫秒）
- 返回: Promise<Object|null>

##### `waitForVerificationCode(pollInterval = 5000, maxWaitTime = 300000, emailCount = 5)`
轮询等待验证码。

- `pollInterval`: 轮询间隔（毫秒）
- `maxWaitTime`: 最大等待时间（毫秒）
- `emailCount`: 每次检查的邮件数量
- 返回: Promise<Object|null>

##### `getVerificationCodeFlow(generatedEmail = null, options = {})`
完整的验证码获取流程。

- `generatedEmail`: 可选的邮箱地址
- `options`: 配置选项
- 返回: Promise<Object>

## 配置信息

插件使用以下IMAP配置连接到QQ邮箱：

- **服务器**: imap.qq.com
- **端口**: 993
- **加密**: TLS
- **邮箱**: <EMAIL>
- **授权码**: okwbkuwqovuschdg

## 验证码格式

插件支持以下验证码格式：

- `Your verification code is: 602238`
- `verification code is: 123456`
- `验证码：602238`
- `code: 123456`
- `602238 is your verification code`

## 运行示例

### 基础示例
```bash
node example.js
```

### 完整示例（包括网络连接）
```bash
node example.js --full
```

### 等待验证码示例
```bash
node example.js --wait
```

### 搜索验证码示例
```bash
node example.js --search
```

## 运行测试

```bash
npm test
# 或
node test.js
```

## 文件结构

```
email-verification-plugin/
├── index.js                    # 主插件文件
├── emailGenerator.js           # 邮箱生成器
├── imapReceiver.js            # IMAP邮件接收器
├── verificationCodeExtractor.js # 验证码提取器
├── test.js                    # 测试文件
├── example.js                 # 使用示例
├── package.json               # 项目配置
└── README.md                  # 说明文档
```

## 注意事项

1. 确保网络连接正常，能够访问QQ邮箱IMAP服务器
2. 验证码必须是6位数字格式
3. 插件会自动处理HTML邮件内容
4. 建议在生产环境中使用环境变量存储邮箱凭据
5. 轮询间隔不要设置太短，避免对服务器造成压力

## 错误处理

插件包含完善的错误处理机制：

- IMAP连接错误
- 邮件解析错误
- 验证码提取失败
- 网络超时等

## 许可证

MIT License

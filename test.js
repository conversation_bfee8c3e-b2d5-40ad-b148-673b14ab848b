const EmailVerificationPlugin = require('./index');
const EmailGenerator = require('./emailGenerator');
const VerificationCodeExtractor = require('./verificationCodeExtractor');

class TestRunner {
    constructor() {
        this.tests = [];
        this.passed = 0;
        this.failed = 0;
    }

    addTest(name, testFunction) {
        this.tests.push({ name, testFunction });
    }

    async runTests() {
        console.log('🧪 开始运行测试...\n');

        for (const test of this.tests) {
            try {
                console.log(`▶️  运行测试: ${test.name}`);
                await test.testFunction();
                console.log(`✅ 通过: ${test.name}\n`);
                this.passed++;
            } catch (error) {
                console.log(`❌ 失败: ${test.name}`);
                console.log(`   错误: ${error.message}\n`);
                this.failed++;
            }
        }

        console.log('📊 测试结果:');
        console.log(`   通过: ${this.passed}`);
        console.log(`   失败: ${this.failed}`);
        console.log(`   总计: ${this.tests.length}`);
    }

    assert(condition, message) {
        if (!condition) {
            throw new Error(message);
        }
    }
}

// 创建测试实例
const testRunner = new TestRunner();

// 测试邮箱生成器
testRunner.addTest('邮箱生成器 - 生成单个邮箱', () => {
    const generator = new EmailGenerator();
    const email = generator.generateRandomEmail();
    
    testRunner.assert(email.endsWith('@shusj.xyz'), '邮箱应该以@shusj.xyz结尾');
    testRunner.assert(email.length > '@shusj.xyz'.length, '邮箱应该有用户名部分');
    
    const username = email.split('@')[0];
    testRunner.assert(username.length === 8, '默认用户名长度应该是8');
    testRunner.assert(/^[a-z0-9]+$/.test(username), '用户名应该只包含小写字母和数字');
});

testRunner.addTest('邮箱生成器 - 生成多个邮箱', () => {
    const generator = new EmailGenerator();
    const emails = generator.generateMultipleEmails(3, 6);
    
    testRunner.assert(emails.length === 3, '应该生成3个邮箱');
    
    emails.forEach(email => {
        testRunner.assert(email.endsWith('@shusj.xyz'), '每个邮箱都应该以@shusj.xyz结尾');
        const username = email.split('@')[0];
        testRunner.assert(username.length === 6, '用户名长度应该是6');
    });
    
    // 检查邮箱是否唯一
    const uniqueEmails = new Set(emails);
    testRunner.assert(uniqueEmails.size === emails.length, '生成的邮箱应该是唯一的');
});

testRunner.addTest('邮箱生成器 - 带前缀生成', () => {
    const generator = new EmailGenerator();
    const email = generator.generateEmailWithPrefix('test', 4);
    
    testRunner.assert(email.startsWith('test'), '邮箱应该以指定前缀开始');
    testRunner.assert(email.endsWith('@shusj.xyz'), '邮箱应该以@shusj.xyz结尾');
    
    const username = email.split('@')[0];
    testRunner.assert(username.length === 8, '用户名总长度应该是前缀+随机部分');
});

// 测试验证码提取器
testRunner.addTest('验证码提取器 - 标准格式', () => {
    const extractor = new VerificationCodeExtractor();
    
    const testCases = [
        'Your verification code is: 602238',
        'Your verification code is:602238',
        'verification code is: 123456',
        'Your verification code is: 999999\n\nThanks!'
    ];
    
    const expectedCodes = ['602238', '602238', '123456', '999999'];
    
    testCases.forEach((content, index) => {
        const code = extractor.extractVerificationCode(content);
        testRunner.assert(code === expectedCodes[index], 
            `应该提取到验证码 ${expectedCodes[index]}，实际得到 ${code}`);
    });
});

testRunner.addTest('验证码提取器 - HTML内容', () => {
    const extractor = new VerificationCodeExtractor();
    
    const htmlContent = `
        <html>
            <body>
                <p>Your verification code is: <strong>602238</strong></p>
                <p>If you are having any issues with your account, please don't hesitate to contact us.</p>
            </body>
        </html>
    `;
    
    const code = extractor.extractVerificationCode(htmlContent);
    testRunner.assert(code === '602238', '应该从HTML内容中提取到验证码');
});

testRunner.addTest('验证码提取器 - 无效内容', () => {
    const extractor = new VerificationCodeExtractor();
    
    const invalidCases = [
        'No verification code here',
        'Your code is: abc123',
        'verification code is: 12345',  // 只有5位
        'verification code is: 1234567', // 7位
        ''
    ];
    
    invalidCases.forEach(content => {
        const code = extractor.extractVerificationCode(content);
        testRunner.assert(code === null, `无效内容应该返回null: "${content}"`);
    });
});

testRunner.addTest('验证码提取器 - 验证码格式验证', () => {
    const extractor = new VerificationCodeExtractor();
    
    testRunner.assert(extractor.isValidVerificationCode('602238'), '602238应该是有效的验证码');
    testRunner.assert(extractor.isValidVerificationCode('000000'), '000000应该是有效的验证码');
    testRunner.assert(!extractor.isValidVerificationCode('12345'), '12345应该是无效的验证码（5位）');
    testRunner.assert(!extractor.isValidVerificationCode('1234567'), '1234567应该是无效的验证码（7位）');
    testRunner.assert(!extractor.isValidVerificationCode('abc123'), 'abc123应该是无效的验证码（包含字母）');
});

// 测试主插件功能（不需要实际连接）
testRunner.addTest('主插件 - 初始化', () => {
    const plugin = new EmailVerificationPlugin();
    
    testRunner.assert(plugin.emailGenerator !== undefined, '应该初始化邮箱生成器');
    testRunner.assert(plugin.imapReceiver !== undefined, '应该初始化IMAP接收器');
    testRunner.assert(plugin.codeExtractor !== undefined, '应该初始化验证码提取器');
});

testRunner.addTest('主插件 - 生成邮箱功能', () => {
    const plugin = new EmailVerificationPlugin();
    
    const email = plugin.generateRandomEmail();
    testRunner.assert(email.endsWith('@shusj.xyz'), '生成的邮箱应该有正确的后缀');
    
    const emails = plugin.generateMultipleEmails(2);
    testRunner.assert(emails.length === 2, '应该生成指定数量的邮箱');
});

// 运行所有测试
if (require.main === module) {
    testRunner.runTests().then(() => {
        console.log('\n🎉 测试完成!');
        
        if (testRunner.failed > 0) {
            process.exit(1);
        }
    }).catch(error => {
        console.error('测试运行出错:', error);
        process.exit(1);
    });
}

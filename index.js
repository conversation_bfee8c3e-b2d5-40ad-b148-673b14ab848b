const EmailGenerator = require('./emailGenerator');
const ImapReceiver = require('./imapReceiver');
const VerificationCodeExtractor = require('./verificationCodeExtractor');

class EmailVerificationPlugin {
    constructor() {
        this.emailGenerator = new EmailGenerator();
        this.imapReceiver = new ImapReceiver();
        this.codeExtractor = new VerificationCodeExtractor();
    }

    /**
     * 生成随机邮箱地址
     * @param {number} length - 用户名长度，默认为8
     * @returns {string} 随机邮箱地址
     */
    generateRandomEmail(length = 8) {
        return this.emailGenerator.generateRandomEmail(length);
    }

    /**
     * 生成多个随机邮箱地址
     * @param {number} count - 生成数量
     * @param {number} length - 用户名长度
     * @returns {Array<string>} 邮箱地址数组
     */
    generateMultipleEmails(count = 1, length = 8) {
        return this.emailGenerator.generateMultipleEmails(count, length);
    }

    /**
     * 连接到邮箱服务器
     * @returns {Promise} 连接Promise
     */
    async connect() {
        try {
            await this.imapReceiver.connect();
            console.log('邮箱连接成功');
            return true;
        } catch (error) {
            console.error('邮箱连接失败:', error);
            throw error;
        }
    }

    /**
     * 断开邮箱连接
     */
    disconnect() {
        this.imapReceiver.disconnect();
        console.log('邮箱连接已断开');
    }

    /**
     * 获取最新的验证码
     * @param {number} emailCount - 检查的邮件数量，默认为10
     * @param {number} maxWaitTime - 最大等待时间（毫秒），默认为30秒
     * @returns {Promise<Object|null>} 验证码信息或null
     */
    async getLatestVerificationCode(emailCount = 10, maxWaitTime = 30000) {
        try {
            console.log('正在获取最新邮件...');
            const emails = await this.imapReceiver.getLatestEmails(emailCount);

            if (emails.length === 0) {
                console.log('没有找到邮件');
                return null;
            }

            console.log(`找到 ${emails.length} 封邮件，正在提取验证码...`);
            const result = this.codeExtractor.getLatestVerificationCode(emails);

            if (result && result.verificationCode) {
                console.log(`找到验证码: ${result.verificationCode}`);
                return {
                    verificationCode: result.verificationCode,
                    subject: result.subject,
                    from: result.from,
                    date: result.date
                };
            } else {
                console.log('未找到验证码');
                return null;
            }
        } catch (error) {
            console.error('获取验证码时出错:', error);
            throw error;
        }
    }

    /**
     * 获取最新一封邮件的验证码（实时获取）
     * @returns {Promise<Object|null>} 验证码信息或null
     */
    async getLatestEmailVerificationCode() {
        try {
            console.log('正在获取最新一封邮件...');
            const email = await this.imapReceiver.getLatestEmail();

            if (!email) {
                console.log('没有找到邮件');
                return null;
            }

            console.log(`获取到最新邮件，主题: ${email.subject}，正在提取验证码...`);
            const result = this.codeExtractor.extractFromSingleEmail(email);

            if (result && result.verificationCode) {
                console.log(`找到验证码: ${result.verificationCode}`);
                return {
                    verificationCode: result.verificationCode,
                    subject: result.subject,
                    from: result.from,
                    date: result.date
                };
            } else {
                console.log('最新邮件中未找到验证码');
                return null;
            }
        } catch (error) {
            console.error('获取最新邮件验证码时出错:', error);
            throw error;
        }
    }

    /**
     * 等待并获取新的验证码（轮询模式）
     * @param {number} pollInterval - 轮询间隔（毫秒），默认为3秒
     * @param {number} maxWaitTime - 最大等待时间（毫秒），默认为5分钟
     * @returns {Promise<Object|null>} 验证码信息或null
     */
    async waitForNewVerificationCode(pollInterval = 3000, maxWaitTime = 300000) {
        const startTime = Date.now();

        console.log(`开始等待新验证码，轮询间隔: ${pollInterval/1000}秒，最大等待时间: ${maxWaitTime/1000}秒`);

        // 记录开始时的最新邮件，用于对比
        let lastEmailSeqno = 0;
        try {
            const initialEmail = await this.imapReceiver.getLatestEmail();
            if (initialEmail) {
                lastEmailSeqno = initialEmail.seqno;
                console.log(`当前最新邮件序号: ${lastEmailSeqno}`);
            }
        } catch (error) {
            console.log('获取初始邮件状态失败，将从头开始监听');
        }

        while (Date.now() - startTime < maxWaitTime) {
            try {
                // 获取最新邮件
                const latestEmail = await this.imapReceiver.getLatestEmail();

                if (latestEmail && latestEmail.seqno > lastEmailSeqno) {
                    console.log(`发现新邮件！序号: ${latestEmail.seqno}, 主题: ${latestEmail.subject}`);

                    // 检查新邮件是否包含验证码
                    const result = this.codeExtractor.extractFromSingleEmail(latestEmail);

                    if (result && result.verificationCode) {
                        console.log(`🎉 在新邮件中找到验证码: ${result.verificationCode}`);
                        return result;
                    } else {
                        console.log('新邮件中未找到验证码，继续等待...');
                        lastEmailSeqno = latestEmail.seqno; // 更新最新邮件序号
                    }
                } else {
                    console.log('暂无新邮件，继续等待...');
                }

                await this.sleep(pollInterval);

            } catch (error) {
                console.error('轮询过程中出错:', error);
                await this.sleep(pollInterval);
            }
        }

        console.log('等待超时，未收到包含验证码的新邮件');
        return null;
    }

    /**
     * 等待并获取验证码（轮询模式）- 兼容旧版本
     * @param {number} pollInterval - 轮询间隔（毫秒），默认为5秒
     * @param {number} maxWaitTime - 最大等待时间（毫秒），默认为5分钟
     * @param {boolean} checkLatestOnly - 是否只检查最新一封邮件，默认为true
     * @returns {Promise<Object|null>} 验证码信息或null
     */
    async waitForVerificationCode(pollInterval = 5000, maxWaitTime = 300000, checkLatestOnly = true) {
        // 使用新的等待新验证码方法
        return await this.waitForNewVerificationCode(pollInterval, maxWaitTime);
    }

    /**
     * 搜索包含验证码的邮件
     * @param {string} searchText - 搜索关键词，默认为"verification code"
     * @param {number} days - 搜索最近几天的邮件，默认为1天
     * @returns {Promise<Array>} 包含验证码的邮件数组
     */
    async searchVerificationEmails(searchText = 'verification code', days = 1) {
        try {
            console.log(`搜索包含"${searchText}"的邮件...`);
            const emails = await this.imapReceiver.searchEmails(searchText, days);
            
            if (emails.length === 0) {
                console.log('没有找到匹配的邮件');
                return [];
            }

            console.log(`找到 ${emails.length} 封匹配邮件，正在提取验证码...`);
            const results = this.codeExtractor.extractFromMultipleEmails(emails);
            
            return results.filter(result => result.found);
        } catch (error) {
            console.error('搜索邮件时出错:', error);
            throw error;
        }
    }

    /**
     * 完整的验证码获取流程
     * @param {string} generatedEmail - 可选的生成邮箱地址（用于记录）
     * @param {Object} options - 配置选项
     * @returns {Promise<Object>} 包含邮箱地址和验证码的结果
     */
    async getVerificationCodeFlow(generatedEmail = null, options = {}) {
        const {
            pollInterval = 3000,    // 3秒轮询一次，更快响应
            maxWaitTime = 300000    // 默认等待5分钟
        } = options;

        try {
            // 生成邮箱地址（如果没有提供）
            const email = generatedEmail || this.generateRandomEmail();
            console.log(`📧 生成的邮箱地址: ${email}`);
            console.log(`💡 请使用此邮箱地址进行注册，验证码将发送到QQ邮箱`);

            // 连接到邮箱服务器
            console.log(`\n🔗 连接到邮箱服务器...`);
            await this.connect();

            // 等待新的验证码
            console.log(`⏰ 开始监听新的验证码邮件...`);
            const result = await this.waitForNewVerificationCode(pollInterval, maxWaitTime);

            // 断开连接
            this.disconnect();

            return {
                email: email,
                verificationCode: result ? result.verificationCode : null,
                emailDetails: result,
                success: !!result
            };

        } catch (error) {
            this.disconnect();
            throw error;
        }
    }

    /**
     * 快速获取验证码流程（用于注册后立即获取）
     * @param {string} generatedEmail - 生成的邮箱地址
     * @param {Object} options - 配置选项
     * @returns {Promise<Object>} 验证码结果
     */
    async quickGetVerificationCode(generatedEmail, options = {}) {
        const {
            pollInterval = 2000,    // 2秒轮询一次，更快
            maxWaitTime = 120000    // 2分钟超时
        } = options;

        console.log(`🚀 快速获取模式启动`);
        console.log(`📧 监听邮箱: ${generatedEmail}`);

        try {
            await this.connect();
            const result = await this.waitForNewVerificationCode(pollInterval, maxWaitTime);
            this.disconnect();

            return {
                email: generatedEmail,
                verificationCode: result ? result.verificationCode : null,
                emailDetails: result,
                success: !!result
            };
        } catch (error) {
            this.disconnect();
            throw error;
        }
    }

    /**
     * 工具方法：延时
     * @param {number} ms - 延时毫秒数
     * @returns {Promise}
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = EmailVerificationPlugin;

// 如果直接运行此文件，执行示例
if (require.main === module) {
    async function example() {
        const plugin = new EmailVerificationPlugin();
        
        try {
            console.log('=== 邮箱验证码插件示例 ===');
            
            // 生成随机邮箱
            const randomEmail = plugin.generateRandomEmail();
            console.log(`生成的随机邮箱: ${randomEmail}`);
            
            // 获取验证码
            console.log('\n🎯 开始监听验证码...');
            const result = await plugin.getVerificationCodeFlow(randomEmail, {
                pollInterval: 3000,     // 3秒轮询一次
                maxWaitTime: 60000      // 最大等待1分钟
            });
            
            if (result.success) {
                console.log('\n✅ 成功获取验证码!');
                console.log(`邮箱: ${result.email}`);
                console.log(`验证码: ${result.verificationCode}`);
                console.log(`邮件主题: ${result.emailDetails.subject}`);
            } else {
                console.log('\n❌ 未能获取到验证码');
            }
            
        } catch (error) {
            console.error('\n❌ 执行过程中出错:', error.message);
        }
    }
    
    example();
}

const EmailVerificationPlugin = require('./index');

/**
 * 使用示例：演示如何使用邮箱验证码插件
 */

async function basicExample() {
    console.log('=== 基础使用示例 ===\n');
    
    const plugin = new EmailVerificationPlugin();
    
    // 1. 生成随机邮箱
    console.log('1. 生成随机邮箱地址:');
    const email1 = plugin.generateRandomEmail();
    const email2 = plugin.generateRandomEmail(10); // 10位用户名
    console.log(`   默认长度: ${email1}`);
    console.log(`   10位用户名: ${email2}`);
    
    // 2. 生成多个邮箱
    console.log('\n2. 生成多个邮箱地址:');
    const emails = plugin.generateMultipleEmails(3, 6);
    emails.forEach((email, index) => {
        console.log(`   邮箱${index + 1}: ${email}`);
    });
    
    console.log('\n基础示例完成!\n');
}

async function verificationCodeExample() {
    console.log('=== 验证码获取示例 ===\n');
    
    const plugin = new EmailVerificationPlugin();
    
    try {
        // 生成一个邮箱地址用于演示
        const testEmail = plugin.generateRandomEmail();
        console.log(`生成测试邮箱: ${testEmail}`);
        
        console.log('\n连接到邮箱服务器...');
        await plugin.connect();
        
        console.log('获取最新验证码...');
        const result = await plugin.getLatestVerificationCode(5); // 检查最新5封邮件
        
        if (result) {
            console.log('\n✅ 找到验证码!');
            console.log(`验证码: ${result.verificationCode}`);
            console.log(`邮件主题: ${result.subject}`);
            console.log(`发件人: ${result.from?.text || result.from}`);
            console.log(`时间: ${result.date}`);
        } else {
            console.log('\n❌ 未找到验证码');
            console.log('请确保邮箱中有包含验证码的邮件');
        }
        
        plugin.disconnect();
        
    } catch (error) {
        console.error('\n❌ 获取验证码时出错:', error.message);
        plugin.disconnect();
    }
    
    console.log('\n验证码获取示例完成!\n');
}

async function waitForCodeExample() {
    console.log('=== 等待验证码示例 ===\n');
    
    const plugin = new EmailVerificationPlugin();
    
    try {
        const testEmail = plugin.generateRandomEmail();
        console.log(`生成测试邮箱: ${testEmail}`);
        console.log('\n提示: 现在可以向 <EMAIL> 发送包含验证码的邮件进行测试');
        console.log('邮件内容示例: "Your verification code is: 123456"');
        
        console.log('\n开始等待验证码...');
        
        const result = await plugin.getVerificationCodeFlow(testEmail, {
            pollInterval: 10000,  // 10秒轮询一次
            maxWaitTime: 60000,   // 最大等待1分钟
            emailCount: 3         // 每次检查3封邮件
        });
        
        if (result.success) {
            console.log('\n🎉 成功获取验证码!');
            console.log(`邮箱: ${result.email}`);
            console.log(`验证码: ${result.verificationCode}`);
            console.log(`邮件详情:`, result.emailDetails);
        } else {
            console.log('\n⏰ 等待超时，未收到验证码');
        }
        
    } catch (error) {
        console.error('\n❌ 等待验证码时出错:', error.message);
    }
    
    console.log('\n等待验证码示例完成!\n');
}

async function searchExample() {
    console.log('=== 搜索验证码邮件示例 ===\n');
    
    const plugin = new EmailVerificationPlugin();
    
    try {
        console.log('连接到邮箱服务器...');
        await plugin.connect();
        
        console.log('搜索包含验证码的邮件...');
        const results = await plugin.searchVerificationEmails('verification code', 7); // 搜索最近7天
        
        if (results.length > 0) {
            console.log(`\n✅ 找到 ${results.length} 封包含验证码的邮件:`);
            results.forEach((result, index) => {
                console.log(`\n邮件 ${index + 1}:`);
                console.log(`  验证码: ${result.verificationCode}`);
                console.log(`  主题: ${result.subject}`);
                console.log(`  发件人: ${result.from?.text || result.from}`);
                console.log(`  时间: ${result.date}`);
            });
        } else {
            console.log('\n❌ 未找到包含验证码的邮件');
        }
        
        plugin.disconnect();
        
    } catch (error) {
        console.error('\n❌ 搜索邮件时出错:', error.message);
        plugin.disconnect();
    }
    
    console.log('\n搜索示例完成!\n');
}

async function customExample() {
    console.log('=== 自定义使用示例 ===\n');
    
    const plugin = new EmailVerificationPlugin();
    
    try {
        // 生成带特定前缀的邮箱
        const customEmail = plugin.emailGenerator.generateEmailWithPrefix('myapp', 6);
        console.log(`自定义前缀邮箱: ${customEmail}`);
        
        // 添加自定义验证码匹配模式
        plugin.codeExtractor.addCustomPattern(/验证码[：:]\s*(\d{6})/);
        
        console.log('\n连接邮箱并获取验证码...');
        await plugin.connect();
        
        // 获取验证码，使用自定义参数
        const result = await plugin.getLatestVerificationCode(10);
        
        if (result) {
            console.log('\n✅ 获取成功!');
            console.log(`验证码: ${result.verificationCode}`);
        } else {
            console.log('\n❌ 未找到验证码');
        }
        
        plugin.disconnect();
        
    } catch (error) {
        console.error('\n❌ 自定义示例出错:', error.message);
        plugin.disconnect();
    }
    
    console.log('\n自定义示例完成!\n');
}

// 主函数
async function runExamples() {
    console.log('🚀 邮箱验证码插件使用示例\n');
    console.log('插件功能:');
    console.log('  ✓ 生成随机邮箱地址 (后缀: @shusj.xyz)');
    console.log('  ✓ 连接QQ邮箱IMAP服务器');
    console.log('  ✓ 接收和解析邮件');
    console.log('  ✓ 提取验证码');
    console.log('  ✓ 轮询等待新验证码');
    console.log('  ✓ 搜索历史验证码邮件\n');
    
    // 运行示例
    await basicExample();
    
    // 询问用户是否要运行需要网络连接的示例
    console.log('注意: 以下示例需要连接到邮箱服务器');
    console.log('如果要运行这些示例，请确保网络连接正常\n');
    
    // 这里可以添加用户输入来选择是否运行网络示例
    // 为了演示，我们只运行基础示例
    
    console.log('💡 提示: 要运行完整的验证码获取示例，请执行:');
    console.log('   node example.js --full');
    console.log('\n🎉 示例演示完成!');
}

// 检查命令行参数
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--full')) {
        // 运行完整示例（包括网络连接）
        (async () => {
            await basicExample();
            await verificationCodeExample();
            await searchExample();
            await customExample();
        })();
    } else if (args.includes('--wait')) {
        // 运行等待验证码示例
        waitForCodeExample();
    } else if (args.includes('--search')) {
        // 运行搜索示例
        searchExample();
    } else {
        // 运行基础示例
        runExamples();
    }
}

const EmailVerificationPlugin = require('./index');

/**
 * 实时验证码获取示例
 * 演示正确的使用流程：生成邮箱 → 注册网站 → 监听新验证码
 */

async function demonstrateRealTimeFlow() {
    console.log('🚀 邮箱验证码插件 - 实时获取演示\n');
    
    const plugin = new EmailVerificationPlugin();
    
    try {
        // 步骤1: 生成随机邮箱地址
        console.log('📧 步骤1: 生成随机邮箱地址');
        const randomEmail = plugin.generateRandomEmail();
        console.log(`   生成的邮箱: ${randomEmail}`);
        console.log(`   请复制此邮箱地址用于注册\n`);
        
        // 步骤2: 提示用户操作
        console.log('👤 步骤2: 用户操作');
        console.log('   1. 复制上面的邮箱地址');
        console.log('   2. 前往要注册的网站');
        console.log('   3. 使用该邮箱地址进行注册');
        console.log('   4. 点击发送验证码\n');
        
        // 步骤3: 开始监听验证码
        console.log('⏰ 步骤3: 开始监听新的验证码邮件');
        console.log('   插件将自动监听QQ邮箱中的新邮件...\n');
        
        const result = await plugin.getVerificationCodeFlow(randomEmail, {
            pollInterval: 3000,   // 3秒检查一次
            maxWaitTime: 180000   // 等待3分钟
        });
        
        // 步骤4: 显示结果
        console.log('📊 步骤4: 获取结果');
        if (result.success) {
            console.log('✅ 成功获取验证码!');
            console.log(`   邮箱地址: ${result.email}`);
            console.log(`   验证码: ${result.verificationCode}`);
            console.log(`   邮件主题: ${result.emailDetails.subject}`);
            console.log(`   接收时间: ${result.emailDetails.date}`);
        } else {
            console.log('❌ 未能获取到验证码');
            console.log('   可能原因:');
            console.log('   - 验证码邮件还未发送');
            console.log('   - 邮件中不包含标准格式的验证码');
            console.log('   - 网络连接问题');
        }
        
    } catch (error) {
        console.error('❌ 执行过程中出错:', error.message);
    }
}

async function quickModeDemo() {
    console.log('\n🏃‍♂️ 快速模式演示\n');
    
    const plugin = new EmailVerificationPlugin();
    
    try {
        // 生成邮箱
        const email = plugin.generateRandomEmail();
        console.log(`📧 生成邮箱: ${email}`);
        console.log('💡 假设您已经用此邮箱完成注册，现在等待验证码...\n');
        
        // 快速获取验证码
        const result = await plugin.quickGetVerificationCode(email, {
            pollInterval: 2000,   // 2秒检查一次，更快
            maxWaitTime: 60000    // 1分钟超时
        });
        
        if (result.success) {
            console.log('🎉 快速获取成功!');
            console.log(`验证码: ${result.verificationCode}`);
        } else {
            console.log('⏰ 快速模式超时，未收到验证码');
        }
        
    } catch (error) {
        console.error('❌ 快速模式出错:', error.message);
    }
}

async function batchEmailDemo() {
    console.log('\n📦 批量邮箱生成演示\n');
    
    const plugin = new EmailVerificationPlugin();
    
    // 生成多个邮箱地址
    console.log('📧 生成5个随机邮箱地址:');
    const emails = plugin.generateMultipleEmails(5, 8);
    
    emails.forEach((email, index) => {
        console.log(`   ${index + 1}. ${email}`);
    });
    
    console.log('\n💡 使用建议:');
    console.log('   - 每个邮箱地址都可以独立使用');
    console.log('   - 建议为不同网站使用不同的邮箱地址');
    console.log('   - 所有验证码都会发送到同一个QQ邮箱');
}

async function monitoringDemo() {
    console.log('\n👁️ 持续监听演示\n');
    
    const plugin = new EmailVerificationPlugin();
    
    try {
        console.log('🔗 连接到邮箱服务器...');
        await plugin.connect();
        
        console.log('👀 开始持续监听新邮件...');
        console.log('💡 现在可以用任意邮箱地址注册网站，插件会自动捕获验证码\n');
        
        // 持续监听30秒
        const result = await plugin.waitForNewVerificationCode(3000, 30000);
        
        if (result) {
            console.log('🎯 捕获到新验证码!');
            console.log(`验证码: ${result.verificationCode}`);
            console.log(`邮件主题: ${result.subject}`);
        } else {
            console.log('⏰ 监听超时，未收到新的验证码邮件');
        }
        
        plugin.disconnect();
        
    } catch (error) {
        console.error('❌ 监听过程中出错:', error.message);
        plugin.disconnect();
    }
}

// 主函数
async function main() {
    const args = process.argv.slice(2);
    
    console.log('🎯 邮箱验证码插件 - 实时获取功能\n');
    console.log('使用场景:');
    console.log('  1. 生成随机邮箱地址');
    console.log('  2. 使用邮箱地址注册网站');
    console.log('  3. 自动获取发送到QQ邮箱的验证码\n');
    
    if (args.includes('--quick')) {
        await quickModeDemo();
    } else if (args.includes('--batch')) {
        await batchEmailDemo();
    } else if (args.includes('--monitor')) {
        await monitoringDemo();
    } else if (args.includes('--all')) {
        await demonstrateRealTimeFlow();
        await quickModeDemo();
        await batchEmailDemo();
        await monitoringDemo();
    } else {
        await demonstrateRealTimeFlow();
    }
    
    console.log('\n🎉 演示完成!');
    console.log('\n📖 其他演示模式:');
    console.log('   node example-realtime.js --quick     # 快速模式');
    console.log('   node example-realtime.js --batch     # 批量生成');
    console.log('   node example-realtime.js --monitor   # 持续监听');
    console.log('   node example-realtime.js --all       # 所有演示');
}

// 运行演示
if (require.main === module) {
    main().catch(error => {
        console.error('演示运行失败:', error);
        process.exit(1);
    });
}

class VerificationCodeExtractor {
    constructor() {
        // 验证码匹配模式 - 使用单词边界确保恰好匹配6位数字
        this.patterns = [
            /Your verification code is:\s*(\d{6})\b/i,
            /verification code is:\s*(\d{6})\b/i,
            /验证码[：:]\s*(\d{6})\b/i,
            /code[：:]\s*(\d{6})\b/i,
            /\b(\d{6})\s*is your verification code/i,
            /\b(\d{6})\s*是您的验证码/i
        ];
    }

    /**
     * 从邮件内容中提取验证码
     * @param {string} emailContent - 邮件内容（文本或HTML）
     * @returns {string|null} 提取到的验证码，如果没有找到则返回null
     */
    extractVerificationCode(emailContent) {
        if (!emailContent) {
            return null;
        }

        // 清理HTML标签（如果是HTML内容）
        const cleanContent = this.cleanHtmlContent(emailContent);

        // 尝试所有匹配模式
        for (const pattern of this.patterns) {
            const match = cleanContent.match(pattern);
            if (match && match[1]) {
                return match[1];
            }
        }

        return null;
    }

    /**
     * 从多封邮件中提取验证码
     * @param {Array} emails - 邮件数组
     * @returns {Array} 包含验证码信息的数组
     */
    extractFromMultipleEmails(emails) {
        const results = [];

        emails.forEach((email, index) => {
            const textCode = this.extractVerificationCode(email.text);
            const htmlCode = this.extractVerificationCode(email.html);
            
            const verificationCode = textCode || htmlCode;
            
            results.push({
                emailIndex: index,
                subject: email.subject,
                from: email.from,
                date: email.date,
                verificationCode: verificationCode,
                found: !!verificationCode
            });
        });

        return results;
    }

    /**
     * 获取最新的验证码
     * @param {Array} emails - 邮件数组（按时间排序，最新的在前）
     * @returns {Object|null} 包含验证码的邮件信息，如果没有找到则返回null
     */
    getLatestVerificationCode(emails) {
        // 邮件已经按时间倒序排列，直接遍历找到第一个包含验证码的邮件
        for (let i = 0; i < emails.length; i++) {
            const email = emails[i];
            const textCode = this.extractVerificationCode(email.text);
            const htmlCode = this.extractVerificationCode(email.html);

            const verificationCode = textCode || htmlCode;

            if (verificationCode) {
                return {
                    emailIndex: i,
                    subject: email.subject,
                    from: email.from,
                    date: email.date,
                    verificationCode: verificationCode,
                    found: true
                };
            }
        }

        return null;
    }

    /**
     * 从单封邮件中提取验证码
     * @param {Object} email - 单封邮件对象
     * @returns {Object|null} 包含验证码的信息，如果没有找到则返回null
     */
    extractFromSingleEmail(email) {
        const textCode = this.extractVerificationCode(email.text);
        const htmlCode = this.extractVerificationCode(email.html);

        const verificationCode = textCode || htmlCode;

        if (verificationCode) {
            return {
                subject: email.subject,
                from: email.from,
                date: email.date,
                verificationCode: verificationCode,
                found: true
            };
        }

        return null;
    }

    /**
     * 清理HTML内容，提取纯文本
     * @param {string} htmlContent - HTML内容
     * @returns {string} 清理后的文本内容
     */
    cleanHtmlContent(htmlContent) {
        if (!htmlContent) {
            return '';
        }

        // 移除HTML标签
        let cleanText = htmlContent.replace(/<[^>]*>/g, ' ');
        
        // 解码HTML实体
        cleanText = cleanText
            .replace(/&nbsp;/g, ' ')
            .replace(/&amp;/g, '&')
            .replace(/&lt;/g, '<')
            .replace(/&gt;/g, '>')
            .replace(/&quot;/g, '"')
            .replace(/&#39;/g, "'");
        
        // 清理多余的空白字符
        cleanText = cleanText.replace(/\s+/g, ' ').trim();
        
        return cleanText;
    }

    /**
     * 验证验证码格式
     * @param {string} code - 验证码
     * @returns {boolean} 是否为有效的6位数字验证码
     */
    isValidVerificationCode(code) {
        return /^\d{6}$/.test(code);
    }

    /**
     * 搜索特定发件人的验证码
     * @param {Array} emails - 邮件数组
     * @param {string} senderPattern - 发件人匹配模式（正则表达式字符串）
     * @returns {Array} 匹配发件人的验证码结果
     */
    extractFromSpecificSender(emails, senderPattern) {
        const senderRegex = new RegExp(senderPattern, 'i');
        
        const filteredEmails = emails.filter(email => {
            const fromText = email.from?.text || email.from?.toString() || '';
            return senderRegex.test(fromText);
        });

        return this.extractFromMultipleEmails(filteredEmails);
    }

    /**
     * 添加自定义验证码匹配模式
     * @param {RegExp} pattern - 正则表达式模式
     */
    addCustomPattern(pattern) {
        if (pattern instanceof RegExp) {
            this.patterns.push(pattern);
        }
    }
}

module.exports = VerificationCodeExtractor;

const crypto = require('crypto');

class EmailGenerator {
    constructor() {
        this.domain = '@shusj.xyz';
    }

    /**
     * 生成随机邮箱地址
     * @param {number} length - 邮箱用户名长度，默认为8
     * @returns {string} 完整的邮箱地址
     */
    generateRandomEmail(length = 8) {
        const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let username = '';
        
        for (let i = 0; i < length; i++) {
            const randomIndex = crypto.randomInt(0, characters.length);
            username += characters[randomIndex];
        }
        
        return username + this.domain;
    }

    /**
     * 生成多个随机邮箱地址
     * @param {number} count - 生成邮箱数量
     * @param {number} length - 邮箱用户名长度
     * @returns {Array<string>} 邮箱地址数组
     */
    generateMultipleEmails(count = 1, length = 8) {
        const emails = [];
        for (let i = 0; i < count; i++) {
            emails.push(this.generateRandomEmail(length));
        }
        return emails;
    }

    /**
     * 生成带有特定前缀的随机邮箱
     * @param {string} prefix - 邮箱前缀
     * @param {number} randomLength - 随机部分长度
     * @returns {string} 完整的邮箱地址
     */
    generateEmailWithPrefix(prefix, randomLength = 4) {
        const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let randomPart = '';
        
        for (let i = 0; i < randomLength; i++) {
            const randomIndex = crypto.randomInt(0, characters.length);
            randomPart += characters[randomIndex];
        }
        
        return prefix + randomPart + this.domain;
    }
}

module.exports = EmailGenerator;
